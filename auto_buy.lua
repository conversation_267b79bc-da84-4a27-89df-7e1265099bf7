local WindUI = loadstring(game:HttpGet("https://github.com/Footagesus/WindUI/releases/latest/download/main.lua"))()


local Window = WindUI:CreateWindow({
    Title = "MooreART Auto Buy World // Zero",
    Icon = "rbxassetid://129260712070622",
    IconThemed = true,
    Author = "MooreART",
    Folder = "MooreART",
    Size = UDim2.fromOffset(580, 460),
    Transparent = true,
    Theme = "Dark",
    User = {
        Enabled = true, 
        Callback = function() print("clicked") end, 
        Anonymous = true 
    },
    SideBarWidth = 200,
    
    ScrollBarEnabled = true, 
    

    
    KeySystem = { 
        Key = { "1234", "5678" },
        Note = "Example Key System. \n\nThe Key is '1234' or '5678",
        
        
        
        
        URL = "https://discord.gg/akmj", 
        SaveKey = true, 
    },
})

local Tabs = {}

do
    Tabs.AutoBuyTab = Window:Tab({ Title = "Auto Buy", Icon = "file-cog" })
    Tabs.PerformanceTab = Window:Tab({ Title = "Performance", Icon = "zap" })
    Tabs.SettingsTab = Window:Tab({ Title = "Settings", Icon = "settings" })
end



local items = {
    { name = "NightmareMask", price = 6000000, dye = "BEA182" },
    { name = "FairyWings", price = 5000000, dye = "1F3440" },
    { name = "HollowMask", price = 30000000, dye = "FFADED" },
    { name = "SideHornsWhite", price = 1000, dye = "none" },
    { name = "Item 5", price = 1000, dye = "FFFF00" },
    { name = "IgnisDragonMount", price = 10000000, dye = "none" },
    { name = "DarkWings", price = 4000000, dye = "FFADED" },
{ name = "DarkWings", price = 4000000, dye = "E6E6E6" },
{ name = "DarkWings", price = 4000000, dye = "FFADED" },
{ name = "DarkWings", price = 4000000, dye = "1E1E1E" },
{ name = "DarkWings", price = 4000000, dye = "FFFFFF" },
{ name = "BodyAura", price = 3000000, dye = "FFADED" },
{ name = "BodyAura", price = 3000000, dye = "E6E6E6" },
{ name = "BodyAura", price = 3000000, dye = "1E1E1E" },
{ name = "VampireShades2", price = 1000000, dye = "1E1E1E" },
{ name = "VampireShades2", price = 1000000, dye = "E6E6E6" },
{ name = "VampireShades2", price = 1000000, dye = "FFADED" },
{ name = "DragonStaffShadow", price = 1000000, dye = "none" },
{ name = "ShadowDragonMount", price = 10000000, dye = "none" },
{ name = "WinterfallDragonMount", price = 1000000, dye = "none" },
{ name = "DragonMountVane", price = 1000000, dye = "none" },
{ name = "DarkDragonMount", price = 1000000, dye = "none" },
{ name = "WhiteCloudMount", price = 100000, dye = "none" },
{ name = "FoxMountPink", price = 1000000, dye = "none" },
{ name = "DragonMountBlack", price = 1000000, dye = "none" },
{ name = "DragonMountGold", price = 1000000, dye = "none" },
{ name = "DragonMountSkeletonBlack", price = 1000000, dye = "none" },
{ name = "DragonMountSkeletonRed", price = 1000000, dye = "none" },
{ name = "DragonMountSkeletonWhite", price = 1000000, dye = "none" },
{ name = "DragonMountVane", price = 1000000, dye = "none" },
{ name = "DragonMountWhite", price = 1000000, dye = "none" },
{ name = "EnergyBoardEnvy", price = 5000000, dye = "none" },
{ name = "EnergyBoardWrath", price = 5000000, dye = "none" },
{ name = "FoxMountAnti", price = 1000000, dye = "none" },
{ name = "FoxMountOrange", price = 1000000, dye = "none" },
{ name = "FoxMountWhite", price = 1000000, dye = "none" },
{ name = "FrostedDeerMountTwilight", price = 1000000, dye = "none" },
{ name = "FrostedDeerMountVoid", price = 1000000, dye = "none" },
{ name = "BeserkFox", price = 1000000, dye = "none" },
{ name = "TridentMountBlack", price = 100000, dye = "none" },
{ name = "ArmoredWolfMountRed", price = 1000000, dye = "none" },
{ name = "ArmoredWolfMountPurple", price = 100000, dye = "none" },
{ name = "TricksterAura", price = 5000000, dye = "none" },
{ name = "ShadowAura", price = 5000000, dye = "none" },
{ name = "PopstarAura", price = 5000000, dye = "none" },
{ name = "FrostchillAura", price = 5000000, dye = "none" },
{ name = "AbyssAura", price = 5000000, dye = "none" },
{ name = "BerserkAura", price = 5000000, dye = "none" },
{ name = "DarkGlitchAura", price = 5000000, dye = "none" },
{ name = "EnvyAura", price = 5000000, dye = "none" },
{ name = "EtherealAura", price = 5000000, dye = "none" },
{ name = "ForbiddenAura", price = 5000000, dye = "none" },
{ name = "LeviathanAura", price = 5000000, dye = "none" },
{ name = "ShadowflareAura", price = 10000000, dye = "none" },
{ name = "WrathAura", price = 10000000, dye = "none" },
{ name = "YangAura", price = 10000000, dye = "none" },
{ name = "YinAura", price = 10000000, dye = "none" },
{ name = "InfernalWings", price = 5000000, dye = "E6E6E6" },
{ name = "InfernalWings", price = 5000000, dye = "1E1E1E" },
{ name = "InfernalWings", price = 5000000, dye = "FFADED" },
{ name = "DemonSwordMount", price = 10000000, dye = "FFADED" },
{ name = "DemonSwordMount", price = 10000000, dye = "E6E6E6" },
{ name = "DemonSwordMount", price = 10000000, dye = "1E1E1E" },
{ name = "VampireHat", price = 10000000, dye = "E6E6E6" },
{ name = "VampireHat", price = 10000000, dye = "1E1E1E" },
{ name = "VampireHat", price = 10000000, dye = "FFADED" },
{ name = "BackJacket", price = 10000000, dye = "E6E6E6" },
{ name = "BackJacket", price = 10000000, dye = "1E1E1E" },
{ name = "BackJacket", price = 10000000, dye = "FFADED" },
{ name = "HeartWings2", price = 3000000, dye = "E6E6E6" },
{ name = "HeartWings2", price = 3000000, dye = "1E1E1E" },
{ name = "HeartWings2", price = 3000000, dye = "FFADED" },
{ name = "SwordsmanCloak", price = 3000000, dye = "E6E6E6" },
{ name = "SwordsmanCloak", price = 3000000, dye = "1E1E1E" },
{ name = "SwordsmanCloak", price = 3000000, dye = "FFADED" },
{ name = "DragonKing", price = 5000000, dye = "FFADED" },
{ name = "DragonKing", price = 5000000, dye = "1E1E1E" },
{ name = "DragonKing", price = 5000000, dye = "E6E6E6" },
{ name = "MechaWings", price = 10000000, dye = "FFADED" },
{ name = "MechaWings", price = 10000000, dye = "1E1E1E" },
{ name = "MechaWings", price = 10000000, dye = "E6E6E6" },
{ name = "VampireWings", price = 10000000, dye = "FFADED" },
{ name = "VampireWings", price = 10000000, dye = "1E1E1E" },
{ name = "VampireWings", price = 10000000, dye = "E6E6E6" },
{ name = "ShadowWings", price = 5000000, dye = "FFADED" },
{ name = "ShadowWings", price = 5000000, dye = "E6E6E6" },
{ name = "ShadowWings", price = 5000000, dye = "1E1E1E" },
{ name = "ShadowEyes", price = 5000000, dye = "E6E6E6" },
{ name = "ShadowEyes", price = 5000000, dye = "1E1E1E" },
{ name = "ShadowEyes", price = 5000000, dye = "FFADED" },
{ name = "NinjaCloak", price = 5000000, dye = "FFADED" },
{ name = "NinjaCloak", price = 5000000, dye = "1E1E1E" },
{ name = "NinjaCloak", price = 5000000, dye = "E6E6E6" },
{ name = "SideKatana", price = 5000000, dye = "E6E6E6" },
{ name = "SideKatana", price = 5000000, dye = "FFADED" },
{ name = "SideKatana", price = 5000000, dye = "1E1E1E" },
{ name = "BunnyMask", price = 10000000, dye = "E6E6E6" },
{ name = "BunnyMask", price = 10000000, dye = "1E1E1E" },
{ name = "BunnyMask", price = 10000000, dye = "FFADED" },
{ name = "NightmareArmor", price = 3000000, dye = "E6E6E6" },
{ name = "NightmareArmor", price = 3000000, dye = "FFADED" },
{ name = "NightmareArmor", price = 3000000, dye = "1E1E1E" },
{ name = "Tuxedo2", price = 3000000, dye = "E6E6E6" },
{ name = "Tuxedo2", price = 3000000, dye = "FFADED" },
{ name = "Tuxedo2", price = 3000000, dye = "1E1E1E" },
{ name = "Grimoire", price = 10000000, dye = "1E1E1E" },
{ name = "Grimoire", price = 10000000, dye = "FFADED" },

}


local selectedItems = {}
local isRunning = false
local autoBuyConnection
local searchCount = 0


local CONFIG_FILE = "auto_buy_config.json"
local toggleStates = {}
local toggleObjects = {}

-- Helper function to create unique item identifier
local function getItemId(item)
    return item.name .. "_" .. item.dye
end

local function saveConfig()
    local config = {}
    for itemId, state in pairs(toggleStates) do
        config[itemId] = state
    end

    local success, result = pcall(function()
        local HttpService = game:GetService("HttpService")
        local jsonString = HttpService:JSONEncode(config)
        writefile(CONFIG_FILE, jsonString)
    end)

    if success then
        print("✅ Configuration saved successfully!")
        return true
    else
        print("❌ Failed to save configuration: " .. tostring(result))
        return false
    end
end


local function loadConfig()
    local success, result = pcall(function()
        if isfile(CONFIG_FILE) then
            local jsonString = readfile(CONFIG_FILE)
            local HttpService = game:GetService("HttpService")
            return HttpService:JSONDecode(jsonString)
        else
            return {}
        end
    end)

    if success then
        print("✅ Configuration loaded successfully!")
        return result
    else
        print("❌ Failed to load configuration: " .. tostring(result))
        return {}
    end
end


local function applyConfig(config)

    selectedItems = {}

    print("🔄 Updating toggle states and selectedItems...")


    for _, item in ipairs(items) do
        local itemId = getItemId(item)
        local newState = config[itemId] or false


        toggleStates[itemId] = newState


        if toggleObjects[itemId] and toggleObjects[itemId].Callback then

            toggleObjects[itemId].Callback(newState)


            local toggle = toggleObjects[itemId]


            local visualUpdated = false
            if toggle.SetValue then
                pcall(function() toggle:SetValue(newState) end)
                visualUpdated = true
            elseif toggle.Set then
                pcall(function() toggle:Set(newState) end)
                visualUpdated = true
            elseif toggle.Update then
                pcall(function() toggle:Update(newState) end)
                visualUpdated = true
            elseif toggle.Value ~= nil then
                pcall(function() toggle.Value = newState end)
                visualUpdated = true
            end

            if visualUpdated then
                print("📋 Updated: " .. item.name .. " (Dye: " .. item.dye .. ") = " .. (newState and "ON" or "OFF") .. " ✅")
            else
                print("📋 Internal state updated: " .. item.name .. " (Dye: " .. item.dye .. ") = " .. (newState and "ON" or "OFF") .. " (visual update may require manual toggle)")
            end
        end
    end

    print("🔄 Configuration applied! Selected items count: " .. #selectedItems)
end

local function hexToRGB(hex)
    
    if hex == "none" then
        return nil, nil, nil
    end

    
    hex = hex:gsub("#", "")

    
    local r = tonumber(hex:sub(1, 2), 16) or 0
    local g = tonumber(hex:sub(3, 4), 16) or 0
    local b = tonumber(hex:sub(5, 6), 16) or 0

    return r, g, b
end


local function colorsMatch(r1, g1, b1, r2, g2, b2, tolerance)
    tolerance = tolerance or 5 
    return math.abs(r1 - r2) <= tolerance and
           math.abs(g1 - g2) <= tolerance and
           math.abs(b1 - b2) <= tolerance
end


local function attemptPurchase(shopItem, selectedItem, shopPrice, shopowner)
    print("🛒 PURCHASING: " .. selectedItem.name .. " for " .. shopPrice .. " from " .. shopowner)

    
    local ReplicatedStorage = game:GetService("ReplicatedStorage")
    local Players = game:GetService("Players")

    
    local localPlayer = Players.LocalPlayer
    local playerGui = localPlayer:FindFirstChild("PlayerGui")

    if not playerGui then
        print("❌ Could not find PlayerGui")
        return
    end

    local profile = playerGui:FindFirstChild("Profile")
    if not profile then
        print("❌ Could not find Profile in PlayerGui")
        return
    end

    local currency = profile:FindFirstChild("Currency")
    if not currency then
        print("❌ Could not find Currency in Profile")
        return
    end

    local gold = currency:FindFirstChild("Gold")
    if not gold then
        print("❌ Could not find Gold in Currency")
        return
    end

    local currentGold = gold.Value or 0
    print("💰 Gold Check - Current: " .. currentGold .. ", Required: " .. shopPrice)

    
    if currentGold < shopPrice then
        print("❌ Insufficient gold! Need " .. shopPrice .. " but only have " .. currentGold .. " (Short by " .. (shopPrice - currentGold) .. ")")
        return
    end

    print("✅ Gold check passed! Proceeding with purchase...")

    
    local Buy = ReplicatedStorage.Shared.Shop.Buy 

    
    local targetPlayer = Players:FindFirstChild(shopowner)
    if not targetPlayer then
        print("❌ Could not find player: " .. shopowner)
        return
    end

    
    

    print("🔄 Attempting purchase via Buy:InvokeServer...")
    print("   Player: " .. shopowner)
    print("   Item: " .. shopItem:GetFullName())
    
    local success, result = pcall(function()
        return Buy:InvokeServer(targetPlayer, shopItem)
    end)

    if success then
        if result then
            Window:Notification({
                Title = "Purchase Success",
                Content = "Purchased " .. selectedItem.name .. " for " .. shopPrice .. " from " .. shopowner,
                Duration = 5,
                Icon = "check-circle",
                Background = "rbxassetid://4880569671"
            })
            print("✅ Successfully purchased " .. selectedItem.name .. " for " .. shopPrice .. " from " .. shopowner)
        else
            print("❌ Purchase failed - server returned false/nil")
        end
    else
        print("❌ Purchase error: " .. tostring(result))
    end
end


-- Performance optimization variables
local searchState = {
    currentShopIndex = 1,
    currentItemIndex = 1,
    currentSelectedIndex = 1,
    shopChildren = {},
    isProcessing = false,
    itemsProcessedThisFrame = 0,
    maxItemsPerFrame = 5, -- Limit items processed per frame to prevent lag
    frameYieldThreshold = 3 -- Yield every N items to spread work across frames
}

-- Performance configuration (can be adjusted based on device performance)
local performanceConfig = {
    maxItemsPerFrame = 5,        -- Lower = smoother but slower, Higher = faster but more lag
    enableEarlyExit = false,     -- Stop after first successful purchase
    enableBatchProcessing = true, -- Process multiple items before yielding
    yieldFrequency = 0.016       -- Yield every ~1 frame (60 FPS = 0.016s per frame)
}

-- Pre-compute selectedItems lookup table for O(1) access instead of O(n) iteration
local selectedItemsLookup = {}

local function updateSelectedItemsLookup()
    print("🔄 DEBUG: updateSelectedItemsLookup() called")
    selectedItemsLookup = {}
    local itemCount = 0

    for _, item in ipairs(selectedItems) do
        local key = item.name
        if not selectedItemsLookup[key] then
            selectedItemsLookup[key] = {}
        end
        table.insert(selectedItemsLookup[key], item)
        itemCount = itemCount + 1
    end

    print("📋 DEBUG: Lookup table updated with " .. itemCount .. " items across " ..
          (function() local count = 0; for _ in pairs(selectedItemsLookup) do count = count + 1 end; return count end)() .. " unique names")
end

-- Optimized function to check if an item matches our criteria
local function checkItemMatch(shopItem, selectedItem)
    -- Early exit: check price first (fastest check)
    local askingPrice = shopItem:FindFirstChild("AskingPrice")
    if not askingPrice or not askingPrice.Value then
        return false, "No price"
    end

    local shopPrice = askingPrice.Value
    if shopPrice > selectedItem.price then
        return false, "Price too high (" .. shopPrice .. " > " .. selectedItem.price .. ")"
    end

    -- Check dye matching
    local Dye = shopItem:FindFirstChild("Dye")
    local dyeMatches = false

    if selectedItem.dye == "none" then
        -- Accept any dye or no dye
        dyeMatches = true
    else
        if Dye and Dye.Value then
            local color = Dye.Value
            local shopR = math.floor(color.R * 255)
            local shopG = math.floor(color.G * 255)
            local shopB = math.floor(color.B * 255)
            local targetR, targetG, targetB = hexToRGB(selectedItem.dye)
            dyeMatches = colorsMatch(shopR, shopG, shopB, targetR, targetG, targetB)
            if not dyeMatches then
                return false, "Dye mismatch (shop: " .. shopR .. "," .. shopG .. "," .. shopB .. " vs target: " .. targetR .. "," .. targetG .. "," .. targetB .. ")"
            end
        else
            dyeMatches = false
            return false, "No dye (target requires #" .. selectedItem.dye .. ")"
        end
    end

    return dyeMatches, shopPrice
end

-- Optimized search function with frame yielding and performance improvements
local function searchPlayerShop()
    print("🔍 DEBUG: searchPlayerShop() called - Search #" .. (searchCount + 1))

    local replicatedStorage = game:GetService("ReplicatedStorage")
    local playerShop = replicatedStorage:FindFirstChild("PlayerShops")

    if not playerShop then
        print("❌ DEBUG: PlayerShops not found in ReplicatedStorage")
        return
    end

    print("✅ DEBUG: PlayerShops found with " .. #playerShop:GetChildren() .. " shops")

    -- Prevent multiple concurrent searches
    if searchState.isProcessing then
        print("⏸️ DEBUG: Search already in progress, skipping")
        return
    end

    print("🚀 DEBUG: Starting new search process")
    searchState.isProcessing = true
    searchCount = searchCount + 1

    if searchCount % 10 == 0 then
        print("🔍 Search #" .. searchCount .. " - Still looking for items...")
    end

    -- Update lookup table if selectedItems changed
    print("📋 DEBUG: Updating selectedItems lookup table")
    updateSelectedItemsLookup()

    -- Debug: Show lookup table contents
    local lookupCount = 0
    for itemName, items in pairs(selectedItemsLookup) do
        lookupCount = lookupCount + #items
        print("🔍 DEBUG: Lookup[" .. itemName .. "] = " .. #items .. " variants")
    end
    print("📊 DEBUG: Total items in lookup: " .. lookupCount .. " (selectedItems: " .. #selectedItems .. ")")

    -- Early exit if no items selected
    if #selectedItems == 0 then
        print("❌ DEBUG: No items selected, exiting search")
        searchState.isProcessing = false
        return
    end

    -- Get fresh shop children list
    searchState.shopChildren = playerShop:GetChildren()
    searchState.currentShopIndex = 1
    searchState.itemsProcessedThisFrame = 0

    print("🏪 DEBUG: Found " .. #searchState.shopChildren .. " shops to search")

    -- Use coroutine to process shops with yielding
    local function processShopsCoroutine()
        print("🔄 DEBUG: Starting processShopsCoroutine")
        local shopsProcessed = 0
        local itemsFound = 0
        local matchesChecked = 0

        while searchState.currentShopIndex <= #searchState.shopChildren and isRunning do
            local shopChild = searchState.shopChildren[searchState.currentShopIndex]
            local sellShop = shopChild:FindFirstChild("SellShop")
            local shopowner = shopChild.Name

            shopsProcessed = shopsProcessed + 1

            if shopsProcessed <= 3 then -- Only log first few shops to avoid spam
                print("🏪 DEBUG: Processing shop " .. shopsProcessed .. "/" .. #searchState.shopChildren .. " - " .. shopowner)
            end

            if sellShop then
                local items = sellShop:FindFirstChild("Items")
                if items then
                    local shopItems = items:GetChildren()
                    itemsFound = itemsFound + #shopItems

                    if shopsProcessed <= 3 then
                        print("📦 DEBUG: Shop " .. shopowner .. " has " .. #shopItems .. " items")
                    end

                    for itemIndex, shopItem in ipairs(shopItems) do
                        -- Check if we need to yield to prevent frame drops
                        searchState.itemsProcessedThisFrame = searchState.itemsProcessedThisFrame + 1

                        if searchState.itemsProcessedThisFrame >= performanceConfig.maxItemsPerFrame then
                            searchState.itemsProcessedThisFrame = 0
                            -- Yield to next frame using configurable frequency
                            if shopsProcessed <= 3 then
                                print("⏸️ DEBUG: Yielding after " .. performanceConfig.maxItemsPerFrame .. " items")
                            end
                            task.wait(performanceConfig.yieldFrequency)

                            -- Check if search was stopped while yielding
                            if not isRunning then
                                print("🛑 DEBUG: Search stopped during yield, exiting coroutine")
                                searchState.isProcessing = false
                                return
                            end
                        end

                        -- Use lookup table for O(1) access instead of O(n) iteration
                        local matchingItems = selectedItemsLookup[shopItem.Name]
                        if matchingItems then
                            matchesChecked = matchesChecked + 1
                            if matchesChecked <= 5 then -- Log first few matches
                                print("🎯 DEBUG: Found potential match: " .. shopItem.Name .. " (" .. #matchingItems .. " variants to check)")
                            end

                            for _, selectedItem in ipairs(matchingItems) do
                                local matches, priceOrReason = checkItemMatch(shopItem, selectedItem)

                                if matches then
                                    print("🎯 PERFECT MATCH! Name ✅ Dye ✅ Price ✅")
                                    print("💰 Buying " .. selectedItem.name .. " for " .. priceOrReason .. " (Target: " .. selectedItem.price .. ")")

                                    attemptPurchase(shopItem, selectedItem, priceOrReason, shopowner)

                                    -- Early exit if configured to stop after first purchase
                                    if performanceConfig.enableEarlyExit then
                                        print("🏁 DEBUG: Early exit enabled, stopping search after purchase")
                                        searchState.isProcessing = false
                                        return
                                    end
                                elseif matchesChecked <= 5 then
                                    print("❌ DEBUG: No match - " .. priceOrReason)
                                end
                            end
                        end
                    end
                else
                    if shopsProcessed <= 3 then
                        print("📦 DEBUG: Shop " .. shopowner .. " has no Items folder")
                    end
                end
            else
                if shopsProcessed <= 3 then
                    print("🏪 DEBUG: Shop " .. shopowner .. " has no SellShop")
                end
            end

            searchState.currentShopIndex = searchState.currentShopIndex + 1
        end

        print("✅ DEBUG: Search completed - Shops: " .. shopsProcessed .. ", Items: " .. itemsFound .. ", Matches checked: " .. matchesChecked)
        searchState.isProcessing = false
    end

    -- Start the coroutine
    print("🚀 DEBUG: Spawning processShopsCoroutine")
    task.spawn(processShopsCoroutine)
    print("✅ DEBUG: Coroutine spawned, searchPlayerShop() exiting")
end


local startButton = Tabs.AutoBuyTab:Button({
    Title = "Start Auto Buy",
    Callback = function()
        if isRunning then
            print("Auto-buy is already running!")
            return
        end

        if #selectedItems == 0 then
            print("No items selected!")
            WindUI:Notify({
                Title = "No Items Selected!",
                Content = "No items selected!",
                Icon = "check",
                Duration = 5,
                Background = "rbxassetid://8867158779"
            })
            return
        end
        WindUI:Notify({
            Title = "Auto Buy Started!",
            Content = "Auto-buy has started happy buying!",
            Icon = "check",
            Duration = 5,
            Background = "rbxassetid://4880569671"
        })

        print("🚀 Starting Auto Buy...")
        print("📊 Selected items with target prices and dye colors:")
        for _, item in ipairs(selectedItems) do
            if item.dye == "none" then
                -- print("  • " .. item.name .. ": " .. item.price .. " | Dye: ANY DYE (all colors including undyed)")
            else
                local r, g, b = hexToRGB(item.dye)
                -- print("  • " .. item.name .. ": " .. item.price .. " | Dye: #" .. item.dye .. " RGB(" .. r .. ", " .. g .. ", " .. b .. ")")
            end
        end
        -- print("🔍 Will search PlayerShop every second for items priced BELOW your targets...")

        isRunning = true
        searchCount = 0 

        
        local lastSearchTime = 0
        local runService = game:GetService("RunService")
        print("🔗 DEBUG: Setting up Heartbeat connection for auto-buy")
        autoBuyConnection = runService.Heartbeat:Connect(function()
            if isRunning then
                local currentTime = tick()
                if currentTime - lastSearchTime >= 1 then
                    print("⏰ DEBUG: 1 second elapsed, calling searchPlayerShop() - isRunning: " .. tostring(isRunning))
                    print("📊 DEBUG: Current state - selectedItems: " .. #selectedItems .. ", searchState.isProcessing: " .. tostring(searchState.isProcessing))
                    searchPlayerShop()
                    lastSearchTime = currentTime
                else
                    -- Uncomment for very verbose timing debug
                    -- print("⏱️ DEBUG: Waiting... " .. string.format("%.2f", currentTime - lastSearchTime) .. "s elapsed")
                end
            else
                print("🛑 DEBUG: isRunning is false, skipping search")
            end
        end)

        print("✅ Auto-buy started! Searching every second...")
    end,
})


local stopButton = Tabs.AutoBuyTab:Button({
    Title = "Stop Auto Buy",
    Callback = function()
        if not isRunning then
            print("Auto-buy is not running!")
            return
        end

        WindUI:Notify({
            Title = "Auto Buy Stopped!",
            Content = "Auto-buy has stopped!",
            Icon = "check",
            Duration = 5,
            Background = "rbxassetid://8867158779"
        })
        print("🛑 Stopping Auto Buy...")
        isRunning = false

        if autoBuyConnection then
            autoBuyConnection:Disconnect()
            autoBuyConnection = nil
        end

        print("✅ Auto-buy stopped!")
    end,
})

-- Performance Settings Tab
Tabs.PerformanceTab:Paragraph({
    Title = "Performance Optimization",
    Content = "Adjust these settings to optimize performance based on your device capabilities. Lower values = smoother but slower, Higher values = faster but may cause lag."
})

Tabs.PerformanceTab:Slider({
    Title = "Items Per Frame",
    Description = "Maximum items processed per frame (Lower = smoother)",
    Default = performanceConfig.maxItemsPerFrame,
    Min = 1,
    Max = 20,
    Rounding = 0,
    Callback = function(value)
        performanceConfig.maxItemsPerFrame = value
        print("🔧 Performance: Items per frame set to " .. value)
    end
})

Tabs.PerformanceTab:Slider({
    Title = "Yield Frequency (ms)",
    Description = "Time to wait between frame yields (Lower = more responsive)",
    Default = math.floor(performanceConfig.yieldFrequency * 1000),
    Min = 1,
    Max = 100,
    Rounding = 0,
    Callback = function(value)
        performanceConfig.yieldFrequency = value / 1000
        print("🔧 Performance: Yield frequency set to " .. value .. "ms")
    end
})

Tabs.PerformanceTab:Toggle({
    Title = "Early Exit After Purchase",
    Description = "Stop searching after first successful purchase (prevents duplicate buying)",
    Default = performanceConfig.enableEarlyExit,
    Callback = function(state)
        performanceConfig.enableEarlyExit = state
        print("🔧 Performance: Early exit " .. (state and "enabled" or "disabled"))
    end
})

Tabs.PerformanceTab:Button({
    Title = "Reset to Defaults",
    Description = "Reset all performance settings to default values",
    Callback = function()
        performanceConfig.maxItemsPerFrame = 5
        performanceConfig.enableEarlyExit = false
        performanceConfig.yieldFrequency = 0.016

        WindUI:Notify({
            Title = "Performance Reset!",
            Content = "All performance settings reset to defaults",
            Icon = "refresh-cw",
            Duration = 3,
            Background = "rbxassetid://4880569671"
        })
        print("🔧 Performance settings reset to defaults")
    end
})

Tabs.PerformanceTab:Paragraph({
    Title = "Performance Tips",
    Content = "• Lower 'Items Per Frame' for smoother gameplay\n• Increase 'Yield Frequency' if experiencing lag\n• Enable 'Early Exit' to prevent buying duplicates\n• Monitor frame rate while auto-buying is active"
})

-- Add performance status display
local performanceStatus = Tabs.PerformanceTab:Paragraph({
    Title = "Current Status",
    Content = "Auto-buy not running"
})

-- Function to update performance status
local function updatePerformanceStatus()
    if isRunning then
        local statusText = string.format(
            "🟢 Auto-buy ACTIVE\n" ..
            "📊 Search #%d\n" ..
            "⚙️ Items/Frame: %d\n" ..
            "⏱️ Yield: %dms\n" ..
            "🎯 Early Exit: %s\n" ..
            "🔍 Selected Items: %d",
            searchCount,
            performanceConfig.maxItemsPerFrame,
            math.floor(performanceConfig.yieldFrequency * 1000),
            performanceConfig.enableEarlyExit and "ON" or "OFF",
            #selectedItems
        )
        performanceStatus:Set({
            Title = "Current Status",
            Content = statusText
        })
    else
        performanceStatus:Set({
            Title = "Current Status",
            Content = "🔴 Auto-buy STOPPED"
        })
    end
end

-- Update status every few seconds when running
task.spawn(function()
    while true do
        updatePerformanceStatus()
        task.wait(2) -- Update every 2 seconds
    end
end)

Tabs.SettingsTab:Button({
    Title = "Save Config",
    Callback = function()
        print("💾 Saving current configuration...")
        if saveConfig() then
            WindUI:Notify({
                Title = "Config Saved!",
                Content = "Config saved successfully!",
                Icon = "check",
                Duration = 5,
                Background = "rbxassetid://4880569671"
            })
            print("✅ All toggle states have been saved!")
            print("📁 Config saved to: " .. CONFIG_FILE)
        else
            WindUI:Notify({
                Title = "Config Save Failed!",
                Content = "Failed to save config!",
                Icon = "check",
                Duration = 5,
                Background = "rbxassetid://8867158779"
            })
            print("❌ Failed to save configuration!")
        end
    end,
})


Tabs.SettingsTab:Button({
    Title = "Load Config",
    Callback = function()
        print("📂 Loading saved configuration...")
        local config = loadConfig()
        if config and next(config) then
            applyConfig(config)

            
            local loadedCount = 0
            for _, state in pairs(config) do
                if state then loadedCount = loadedCount + 1 end
            end

            WindUI:Notify({
                Title = "Config Loaded!",
                Content = "Loaded " .. loadedCount .. " item(s) from config!",
                Icon = "check",
                Duration = 5,
                Background = "rbxassetid://4880569671"
            })

            print("✅ Configuration loaded successfully!")
            print("📊 " .. loadedCount .. " item(s) are now internally selected.")
            print("💡 If toggle switches don't update visually, try clicking them once to sync the UI.")
        else
            WindUI:Notify({
                Title = "No Config Found!",
                Content = "No saved config found!",
                Icon = "check",
                Duration = 5,
                Background = "rbxassetid://8867158779"
            })
            print("📝 No saved configuration found or config is empty.")
        end
    end,
})


Tabs.SettingsTab:Button({
    Title = "Show Current Config",
    Callback = function()
        print("📋 Current Configuration:")
        local activeCount = 0
        for _, item in ipairs(items) do
            local itemId = getItemId(item)
            local state = toggleStates[itemId] or false
            if state then
                activeCount = activeCount + 1
                print("  ✅ " .. item.name .. " (Dye: " .. item.dye .. ") - SELECTED")
            else
                print("  ❌ " .. item.name .. " (Dye: " .. item.dye .. ") - NOT SELECTED")
            end
        end
        print("📊 Total selected items: " .. activeCount)
        print("📊 Items in selectedItems array: " .. #selectedItems)
    end,
})

print("🔧 Auto Buy Deal Finder Ready!")
print("📋 Set your maximum prices, select items, and click 'Start Auto Buy' to find deals.")
print("💡 The system will only buy items priced BELOW your configured targets!")
print("💾 Use the Settings tab to save/load your toggle configurations!")


print("📂 Loading saved configuration...")
local savedConfig = loadConfig()


for _, item in ipairs(items) do
    local dyeDisplay = item.dye == "none" and "ANY DYE" or "#" .. item.dye
    local itemId = getItemId(item)


    local defaultState = savedConfig[itemId] or false
    toggleStates[itemId] = defaultState


    if defaultState then
        table.insert(selectedItems, item)
        print("🔄 Restored: " .. item.name .. " (Dye: " .. item.dye .. ") (from saved config)")
    end


    local function toggleCallback(state)
        toggleStates[itemId] = state

        if state then

            local alreadySelected = false
            for i, selectedItem in ipairs(selectedItems) do
                if selectedItem.name == item.name and selectedItem.dye == item.dye then
                    alreadySelected = true
                    break
                end
            end

            if not alreadySelected then
                table.insert(selectedItems, item)
            end

            if item.dye == "none" then
                -- print("Selected: " .. item.name .. " at price " .. item.price .. " with ANY DYE (all colors including undyed)")
            else
                local r, g, b = hexToRGB(item.dye)
                -- print("Selected: " .. item.name .. " at price " .. item.price .. " with dye RGB(" .. r .. ", " .. g .. ", " .. b .. ")")
            end
        else

            for i, selectedItem in ipairs(selectedItems) do
                if selectedItem.name == item.name and selectedItem.dye == item.dye then
                    table.remove(selectedItems, i)
                    print("Deselected: " .. item.name .. " (Dye: " .. item.dye .. ")")
                    break
                end
            end
        end
    end

    local toggle = Tabs.AutoBuyTab:Toggle({
        Title = item.name .. " (Price: " .. item.price .. ", Dye: " .. dyeDisplay .. ")",
        Default = defaultState,
        Callback = toggleCallback
    })


    toggleObjects[itemId] = toggle
    toggleObjects[itemId].Callback = toggleCallback
end


local restoredCount = 0
for _, state in pairs(savedConfig) do
    if state then restoredCount = restoredCount + 1 end
end

if restoredCount > 0 then
    print("✅ Restored " .. restoredCount .. " item(s) from saved configuration!")
else
    print("📝 No saved configuration found - starting with default settings.")
end




